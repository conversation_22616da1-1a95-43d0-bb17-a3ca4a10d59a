# MCP Server Deployment Guide

This comprehensive guide provides multiple deployment methods for the MCP Server project, including Docker Compose deployment. It covers the prerequisites, software requirements, and deployment steps for each method.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Deployment Methods](#deployment-methods)
   - [Docker Compose (Recommended)](#docker-compose-recommended)



## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 22.04+), Windows 10/11, macOS 10.15+
- **Memory**: Minimum 512MB RAM, Recommended 1GB+
- **Storage**: Minimum 2GB free space
- **Network**: Internet access for downloading dependencies

### Software Requirements

#### For Docker Deployments:
- **Docker** (version 20.10 or higher), link: https://docs.docker.com/engine/install/
- **Docker Compose** (version 2.0 or higher), link: https://docs.docker.com/compose/install/  
- **Git** (for cloning the repository), link: https://git-scm.com/downloads
- **uv** (Python virtual environment manager), link: https://docs.astral.sh/uv/getting-started/installation/

### Verify Prerequisites

```bash
# Check Docker version
docker --version

# Check Docker Compose version
docker compose version

# Check uv installation
uv --version

# Check Git version
git --version
```

## Deployment Methods

### Docker Compose (Recommended)

This is the recommended deployment method for most use cases.

#### Step 1: Clone the Repository

```bash
git clone https://<EMAIL>/BnTProjects/AIandBI/_git/MCPServer
cd MCPServer
```

#### Step 2: Environment Configuration

Create and configure the environment file:

```bash
cp .env.example .env
```

Edit the `.env` file with your specific configuration:

```bash
# Edit the .env file
nano .env
```

```bash
# MCP Server Configuration
MCP_ENV=prod                                    # Set to 'prod' for production
MCP_TOKEN=your_secure_mcp_token_here           # Generate a secure token
AUTH_SERVER_URL=https://your-auth-server.com   # Your authorization server URL
RESOURCE_SERVER_URL=https://your-server.com    # This server's public URL

# MySQL Database Configuration
MYSQL_HOST=your_mysql_host                      # MySQL server hostname/IP
MYSQL_PORT=3306                                 # MySQL port (default: 3306)
MYSQL_USER=your_mysql_user                      # MySQL username
MYSQL_PASSWORD=your_secure_mysql_password       # MySQL password
MYSQL_DATABASE=your_database_name               # Database name
```

#### Step 3: Security Configuration

```bash
# Set proper permissions for environment file
chmod 600 .env
```

#### Step 4: Deployment:
```bash
# Build and start the container
docker compose up --build

# Or run in detached mode
docker compose up --build -d
```

